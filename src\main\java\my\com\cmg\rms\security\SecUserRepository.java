package my.com.cmg.rms.security;

import java.util.Optional;
import my.com.cmg.rms.model.RmUser;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface SecUserRepository extends JpaRepository<RmUser, Long> {

  RmUser findByUsername(String username);

  RmUser findByUserEmail(String userEmail);

  Optional<RmUser> findByKeycloakUserId(String keycloakUserId);

  Optional<RmUser> findByPwResetKey(String pwResetKey);
}

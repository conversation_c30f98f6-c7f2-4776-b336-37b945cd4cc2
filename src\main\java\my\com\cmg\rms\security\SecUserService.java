package my.com.cmg.rms.security;

import org.springframework.stereotype.Service;

@Service
public class SecUserService {

  private SecUserRepositoryJooq userRepositoryJooq;

  public SecUserService(SecUserRepositoryJooq userRepositoryJooq) {
    this.userRepositoryJooq = userRepositoryJooq;
  }

  public SecUserDTO getByUserId(Long userId) {
    SecUserDTO u = userRepositoryJooq.findByUserId(userId);
    return u;
  }
}

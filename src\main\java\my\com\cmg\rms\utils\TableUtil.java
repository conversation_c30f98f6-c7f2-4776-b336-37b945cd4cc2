package my.com.cmg.rms.utils;

import org.jooq.Record;
import org.jooq.Table;
import org.jooq.impl.DSL;

public class TableUtil {
  public static final String RM_REF_CODES = "rm_ref_codes";
  public static final String RM_REQUEST_HDR = "rm_request_hdr";
  public static final String RM_REQUEST_DTL = "rm_request_dtl";
  public static final String RM_DOSAGE_FORMS = "rm_dosage_forms";
  public static final String RM_ITEM_CLASS = "rm_item_categories";
  public static final String RM_ITEM_SUBCLASS = "rm_item_subgroups";
  public static final String RM_UOMS = "rm_uoms";
  public static final String RM_REQUEST_FACILITY = "rm_request_facility";
  public static final String RM_REQUEST_SUPPLIER = "rm_request_supplier";
  public static final String RM_REQUEST_ITEMS = "rm_request_items";
  public static final String RM_REQUEST_ITEM_PACKAGING = "rm_request_item_packaging";
  public static final String RM_USER = "rm_user";

  /**
   * @param table required
   * @param alias optional
   * @return table with tableAlias
   */
  public static Table<Record> table(String table, String alias) {
    String tableName = "%s %s";
    if (alias == null) {
      alias = "";
    }
    return DSL.table(String.format(tableName, table, alias));
  }

  public static Table<Record> table(String table) {
    return DSL.table(table);
  }
}

package my.com.cmg.rms.security;

import java.util.Collection;
import java.util.Optional;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;

public class CustomSession extends AbstractAuthenticationToken {

  private final SecUserDTO securityUser;
  private final Jwt jwt;

  public CustomSession(
      SecUserDTO securityUser, Jwt jwt, Collection<? extends GrantedAuthority> authorities) {
    super(authorities);
    this.securityUser = securityUser;
    this.jwt = jwt;
    this.setAuthenticated(true);
  }

  @Override
  public SecUserDTO getPrincipal() {
    return getSecurityUser();
  }

  @Override
  public Jwt getCredentials() {
    return getJwt();
  }

  public Jwt getJwt() {
    return jwt;
  }

  public SecUserDTO getSecurityUser() {
    return securityUser;
  }

  // *********************************************************************
  // * Static Helpers
  // *********************************************************************
  public static Optional<CustomSession> GetSession() {
    return Optional.ofNullable(SecurityContextHolder.getContext().getAuthentication())
        .map(s -> s instanceof CustomSession ? (CustomSession) s : null);
  }

  public static Optional<Jwt> GetJwt() {
    return GetSession().map(CustomSession::getJwt);
  }

  public static Optional<SecUserDTO> GetSecurityUser() {
    return GetSession().map(CustomSession::getSecurityUser);
  }

  // public static Optional<Long> GetSecurityUserId() {
  //   return GetSecurityUser().map(SecUser::userId);
  // }
  // public static Optional<String> GetSecurityUserEmail() {
  // return GetSecurityUser().map(SecurityUser::getEmail);
  // }
}

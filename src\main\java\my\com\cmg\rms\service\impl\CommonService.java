package my.com.cmg.rms.service.impl;

import java.time.LocalDate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.cmg.rms.model.RmUser;
import my.com.cmg.rms.repository.jooq.UtilsRepositoryJooq;
import my.com.cmg.rms.security.SecUserDTO;
import my.com.cmg.rms.security.SecUserRepository;
import my.com.cmg.rms.service.ICommonService;
import my.com.cmg.rms.utils.CommonUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class CommonService implements ICommonService {
  @Value("${phis.development.mode}")
  private String developmentMode;

  private final SecUserRepository secUserRepository;
  private final UtilsRepositoryJooq utilsRepositoryJooq;

  @Override
  public SecUserDTO getAuthenticatedUser(Authentication authentication) {
    String devModeUsername = "adminuser";

    SecUserDTO authenticatedUser = null;
    if (developmentMode.equals("true")) {
      System.out.println("Dev Mode on");
      RmUser currentUser = secUserRepository.findByUsername(devModeUsername);
      authenticatedUser =
          new SecUserDTO(
              currentUser.getUserId(),
              currentUser.getUsername(),
              currentUser.getUserFullName(),
              currentUser.getIdentityType(),
              currentUser.getIdentityNo(),
              currentUser.getUserEmail(),
              currentUser.getUserContactNo(),
              currentUser.getUserDesignation(),
              currentUser.getUserDesignationDesc(),
              currentUser.getUserLocationType(),
              currentUser.getUserLocationTypeDesc(),
              currentUser.getDivision(),
              currentUser.getDivisionDesc(),
              currentUser.getJkn(),
              currentUser.getJknDesc(),
              currentUser.getFacilitySeqno(),
              currentUser.getFacilityDesc(),
              currentUser.getUserType(),
              currentUser.getUserTypeDesc(),
              currentUser.getDeptSeqno(),
              currentUser.getDeptDesc(),
              currentUser.getPrescriberSeqno(),
              currentUser.getPrescriberDesc(),
              currentUser.getRegistrationSeqno(),
              currentUser.getSpecialtySeqno(),
              currentUser.getSpecialtyDesc(),
              currentUser.getKeycloakUserId(),
              currentUser.getPwResetKey());

    } else {
      authenticatedUser =
          CommonUtil.isExist(authentication) ? (SecUserDTO) authentication.getPrincipal() : null;
    }
    return authenticatedUser;
  }

  @Override
  public String getRequestNo() {
    // TODO: scheduler to clear sequence every year
    LocalDate currentDate = LocalDate.now();
    Long sequenceNo = utilsRepositoryJooq.getSequenceNo("rm_request_no_adh_seq");

    String formattedSequenceNo = String.format("%07d", sequenceNo);
    return String.format("%s%sD", String.valueOf(currentDate.getYear()), formattedSequenceNo);
  }
}

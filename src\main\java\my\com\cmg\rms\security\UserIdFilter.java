package my.com.cmg.rms.security;

import com.nimbusds.jwt.JWT;
import com.nimbusds.jwt.JWTParser;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.ParseException;
import org.springframework.lang.NonNull;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.web.filter.OncePerRequestFilter;

public class UserIdFilter extends OncePerRequestFilter {
  private final SecUserService userService;
  private final JedisService jb;

  public UserIdFilter(SecUserService userService, JedisService jb) {
    this.userService = userService;
    this.jb = jb;
  }

  @Override
  protected void doFilterInternal(
      @NonNull HttpServletRequest request,
      @NonNull HttpServletResponse response,
      @NonNull FilterChain filterChain)
      throws ServletException, IOException {
    String auth = request.getHeader("Authorization");
    String[] authHeader = auth.split(" ");
    String sid = "";
    String token = authHeader[1];

    try {
      JWT jwt = JWTParser.parse(token);
      sid = jwt.getJWTClaimsSet().getClaim("sid").toString();
    } catch (ParseException e) {
      // TODO Auto-generated catch block
      e.printStackTrace();
    } finally {

    }

    String refId = jb.getHashData(sid, "refId");

    if (refId != null) {
      Long userId = Long.valueOf(refId);
      SecUserDTO user = userService.getByUserId(userId);
      System.out.println("--------UserIdFilter: " + user);
      if (user != null) {
        // Create an authentication token and set it in the security context
        CustomSession authentication = new CustomSession(user, null, null);
        authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
        SecurityContextHolder.getContext().setAuthentication(authentication);
        filterChain.doFilter(request, response);

      } else {
        SecurityContextHolder.clearContext();
        System.out.println("------ INVALID USER ID ------");
        System.out.println("------ SEND UNAUTHORIZED ------");
        response.sendError(HttpServletResponse.SC_UNAUTHORIZED, "Invalid User ID format");
      }
    } else {
      SecurityContextHolder.clearContext();
      response.sendError(HttpServletResponse.SC_UNAUTHORIZED, "Invalid User ID format");
    }
  }
}

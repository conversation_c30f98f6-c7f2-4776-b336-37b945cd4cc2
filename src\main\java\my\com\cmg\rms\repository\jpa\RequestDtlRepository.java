package my.com.cmg.rms.repository.jpa;

import java.util.List;
import my.com.cmg.rms.model.RequestDtl;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface RequestDtlRepository extends JpaRepository<RequestDtl, Long> {

  List<RequestDtl> findByRequestHdrRequestHdrSeqno(Long requestHdrSeqno);

  @Query(
      value = "SELECT MAX(trans_seqno) FROM rm_request_dtl WHERE request_hdr_seqno = :hdrSeqno",
      nativeQuery = true)
  Long findMaxTransSeqnoByHdrSeqno(@Param("hdrSeqno") Long hdrSeqno);

  default Long getNextTransSeqno(Long hdrSeqno) {
    Long max = findMaxTransSeqnoByHdrSeqno(hdrSeqno);
    return (max != null ? max + 1 : 1L);
  }
}

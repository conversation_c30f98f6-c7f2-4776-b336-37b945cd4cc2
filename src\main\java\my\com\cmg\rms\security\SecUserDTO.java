package my.com.cmg.rms.security;

public record SecUserDTO(
    Long userId,
    String username,
    String userFullName,
    String identityType,
    String identityNo,
    String userEmail,
    String userContactNo,
    String userDesignation,
    String userDesignationDesc,
    String userLocationType,
    String userLocationTypeDesc,
    String division,
    String divisionDesc,
    String jkn,
    String jknDesc,
    Long facilitySeqno,
    String facilityDesc,
    String userType,
    String userTypeDesc,
    Long deptSeqno,
    String deptDesc,
    Long prescriberSeqno,
    String prescriberDesc,
    Long registrationSeqno,
    Long specialtySeqno,
    String specialtyDesc,
    String keycloakUserId,
    String pwResetKey) {}

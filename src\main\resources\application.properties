spring.application.name=rms

spring.datasource.url=************************************
spring.jpa.properties.hibernate.default_schema=phisprod
spring.datasource.username=ihisblank
spring.datasource.password=ihisblank

spring.security.oauth2.resourceserver.jwt.issuer-uri=http://*********:8090/realms/phis-cloud
phis.development.mode=true

phis.redis.host=**********
phis.redis.port=6379

phis.environment=production
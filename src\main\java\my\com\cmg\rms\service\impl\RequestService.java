package my.com.cmg.rms.service.impl;

import static my.com.cmg.rms.constant.RmsConstant.*;

import jakarta.transaction.Transactional;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.cmg.rms.constant.RmsConstant;
import my.com.cmg.rms.dto.RequestDtlDTO;
import my.com.cmg.rms.dto.RequestHeaderDTO;
import my.com.cmg.rms.dto.RequestHeaderDtlDTO;
import my.com.cmg.rms.dto.paging.PaginationRequestDTO;
import my.com.cmg.rms.dto.paging.PaginationResponseDTO;
import my.com.cmg.rms.dto.requestDetail.FacilityMasterDetailDTO;
import my.com.cmg.rms.dto.requestDetail.ItemMasterDetailDTO;
import my.com.cmg.rms.dto.requestDetail.ItemPackagingDetailDTO;
import my.com.cmg.rms.dto.requestDetail.SaveRequestDTO;
import my.com.cmg.rms.dto.requestDetail.SupplierMasterDetailDTO;
import my.com.cmg.rms.dto.requestListing.RequestListDTO;
import my.com.cmg.rms.dto.requestListing.RequestListSearchDTO;
import my.com.cmg.rms.dto.viewDetail.FacilityMasterRequestDTO;
import my.com.cmg.rms.dto.viewDetail.ItemMasterRequestDTO;
import my.com.cmg.rms.dto.viewDetail.ItemPackagingRequestDTO;
import my.com.cmg.rms.dto.viewDetail.SupplierMasterRequestDTO;
import my.com.cmg.rms.dto.viewDetail.ViewRequestDTO;
import my.com.cmg.rms.exception.ExceptionCode;
import my.com.cmg.rms.exception.RmsException;
import my.com.cmg.rms.mapper.RequestMapper;
import my.com.cmg.rms.model.RequestDtl;
import my.com.cmg.rms.model.RequestFacility;
import my.com.cmg.rms.model.RequestHdr;
import my.com.cmg.rms.model.RequestItemMaster;
import my.com.cmg.rms.model.RequestItemPackaging;
import my.com.cmg.rms.model.RequestSupplier;
import my.com.cmg.rms.repository.jooq.RequestRepositoryJooq;
import my.com.cmg.rms.repository.jpa.RequestDtlRepository;
import my.com.cmg.rms.repository.jpa.RequestFacilityRepository;
import my.com.cmg.rms.repository.jpa.RequestHdrRepository;
import my.com.cmg.rms.repository.jpa.RequestItemMasterRepository;
import my.com.cmg.rms.repository.jpa.RequestItemPackagingRepository;
import my.com.cmg.rms.repository.jpa.RequestSupplierRepository;
import my.com.cmg.rms.security.SecUserDTO;
import my.com.cmg.rms.service.ICommonService;
import my.com.cmg.rms.service.IRequestService;
import my.com.cmg.rms.utils.PaginationUtil;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class RequestService implements IRequestService {

  private final RequestRepositoryJooq requestRepositoryJooq;
  private final RequestHdrRepository requestHdrRepository;
  private final RequestDtlRepository requestDtlRepository;
  private final RequestSupplierRepository requestSupplierRepository;
  private final RequestFacilityRepository requestFacilityRepository;
  private final RequestItemMasterRepository requestItemMasterRepository;
  private final RequestItemPackagingRepository requestItemPackagingRepository;
  private final ICommonService commonService;

  @Override
  @Transactional
  public Long save(SaveRequestDTO dto, SecUserDTO user) {
    RequestHdr hdr = saveRequestHeader(dto.requestHeaderDtl(), user.userId());
    String requestType = dto.requestHeaderDtl().requestType();
    String subCategory = dto.requestHeaderDtl().subCategory();

    boolean isNew = DATA_REQUEST_TYPE_NEW.toString().equalsIgnoreCase(requestType);
    boolean isUpdate = DATA_REQUEST_TYPE_UPDATE.toString().equalsIgnoreCase(requestType);

    if (isUpdate && dto.requestDtl() != null) {
      saveRequestDetail(dto.requestDtl(), hdr, user);
      if (DATA_REQUEST_SUB_CATEGORY_NEW_SUPPLIER_MASTER.equalsIgnoreCase(subCategory)) {
        saveSupplier(dto.supplierMaster(), hdr, user);
      } else if (DATA_REQUEST_SUB_CATEGORY_NEW_ITEM_MASTER.equalsIgnoreCase(subCategory)) {
        saveItem(dto.itemMaster(), hdr, user);
      } else if (DATA_REQUEST_SUB_CATEGORY_NEW_ITEM_PACKAGING.equalsIgnoreCase(subCategory)) {
        savePackaging(dto.itemPackaging(), hdr, user);
      }
    }

    if (isNew) {
      if (dto.requestDtl() != null) {
        saveRequestDetail(dto.requestDtl(), hdr, user);
      }
      if (DATA_REQUEST_SUB_CATEGORY_NEW_SUPPLIER_MASTER.equalsIgnoreCase(subCategory)) {
        saveSupplier(dto.supplierMaster(), hdr, user);
      } else if (DATA_REQUEST_SUB_CATEGORY_NEW_ITEM_MASTER.equalsIgnoreCase(subCategory)) {
        saveItem(dto.itemMaster(), hdr, user);
      } else if (DATA_REQUEST_SUB_CATEGORY_NEW_ITEM_PACKAGING.equalsIgnoreCase(subCategory)) {
        savePackaging(dto.itemPackaging(), hdr, user);
      } else if (DATA_REQUEST_SUB_CATEGORY_NEW_FACILITY_MASTER.equalsIgnoreCase(subCategory)) {
        saveFacility(dto.facilityMaster(), hdr, user);
      }
    }

    return hdr.getRequestHdrSeqno();
  }

  private String trim(String input, int maxLength) {
    if (input == null) return null;
    return input.length() > maxLength ? input.substring(0, maxLength) : input;
  }

  @Override
  @Transactional
  public void update(Long requestHdrSeqno, SaveRequestDTO dto, SecUserDTO user) {
    RequestHdr existingHdr =
        requestHdrRepository
            .findById(requestHdrSeqno)
            .orElseThrow(() -> new RmsException(ExceptionCode.ERROR, "Request not found"));

    RequestHeaderDtlDTO headerDTO = dto.requestHeaderDtl();
    existingHdr.setTitle(headerDTO.title());
    existingHdr.setReference(headerDTO.reference());
    existingHdr.setIntention(headerDTO.intention());
    existingHdr.setReason(headerDTO.reason());
    existingHdr.setUpdatedBy(user.userId());
    existingHdr.setStatus("DRAFT");
    existingHdr.setUpdatedDate(LocalDateTime.now());
    requestHdrRepository.save(existingHdr);

    List<RequestDtl> dtls = requestDtlRepository.findByRequestHdrRequestHdrSeqno(requestHdrSeqno);

    if (!dtls.isEmpty() && dto.requestDtl() != null) {
      RequestDtl existingDtl = dtls.get(0);
      RequestDtlDTO dtlDTO = dto.requestDtl();
      existingDtl.setTransDetails(dtlDTO.transDetails());
      existingDtl.setUpdatedBy(user.userId());
      existingDtl.setUpdatedDate(LocalDateTime.now());
      requestDtlRepository.save(existingDtl);
    }
  }

  @Override
  @Transactional
  public void confirmRequest(Long requestHdrSeqno, SecUserDTO user) {
    RequestHdr hdr =
        requestHdrRepository
            .findById(requestHdrSeqno)
            .orElseThrow(() -> new RmsException(ExceptionCode.ERROR, "Request not found"));

    if (!"DRAFT".equalsIgnoreCase(hdr.getStatus())) {
      throw new RmsException(ExceptionCode.ERROR, "Only draft requests can be confirmed");
    }

    hdr.setStatus("PENDING");
    hdr.setUpdatedBy(user.userId());
    hdr.setUpdatedDate(LocalDateTime.now());

    requestHdrRepository.save(hdr);
  }

  private RequestHdr saveRequestHeader(RequestHeaderDtlDTO headerDtlDTO, Long userId) {
    RequestHdr entity = new RequestHdr();
    entity.setRequestNo(commonService.getRequestNo());
    entity.setRequestType(headerDtlDTO.requestType());
    entity.setCategory(headerDtlDTO.category());
    entity.setSubCategory(headerDtlDTO.subCategory());
    entity.setTitle(headerDtlDTO.title());
    entity.setReference(headerDtlDTO.reference());
    entity.setIntention(headerDtlDTO.intention());
    entity.setReason(headerDtlDTO.reason());

    entity.setCreatedBy(userId);
    entity.setUpdatedBy(userId);
    entity.setStatus("DRAFT");
    entity.setCreatedDate(LocalDateTime.now());
    entity.setUpdatedDate(LocalDateTime.now());
    entity.setFacilitySeqno(userId);
    entity.setRequestedBySeqno(userId);
    entity.setRequestedDate(LocalDateTime.now());
    entity.setActiveFlag(RmsConstant.ACTIVE_FLAG_TRUE);

    return requestHdrRepository.save(entity);
  }

  private void saveRequestDetail(RequestDtlDTO dtlDTO, RequestHdr hdr, SecUserDTO user) {
    if (dtlDTO == null) return;

    RequestDtl entity = new RequestDtl();
    entity.setRequestHdr(hdr);

    Long nextTransSeqno = requestDtlRepository.getNextTransSeqno(hdr.getRequestHdrSeqno());
    entity.setTransSeqno(nextTransSeqno);

    entity.setTransCode(dtlDTO.transCode());
    entity.setTransName(dtlDTO.transName());
    entity.setTransType(dtlDTO.transType());
    entity.setTransDetails(dtlDTO.transDetails());
    entity.setCreatedBy(user.userId());
    entity.setUpdatedBy(user.userId());
    entity.setCreatedDate(LocalDateTime.now());
    entity.setUpdatedDate(LocalDateTime.now());
    entity.setActiveFlag(RmsConstant.ACTIVE_FLAG_TRUE);

    requestDtlRepository.save(entity);
  }

  private void saveSupplier(SupplierMasterDetailDTO dto, RequestHdr hdr, SecUserDTO user) {
    RequestSupplier entity = new RequestSupplier();
    entity.setSupplierReqName(trim(dto.supplierReqName(), 100));
    entity.setSupplierReqType(trim(dto.supplierReqType(), 10));
    entity.setSupplierReqTypeDesc(trim(dto.supplierReqType(), 100));
    entity.setCompanyRegNo(trim(dto.companyRegNo(), 20));

    if (dto.regExpiryDate() != null) {
      entity.setRegExpiryDate(dto.regExpiryDate().atStartOfDay());
    }
    entity.setTrsRegNo(dto.trsRegNo());
    entity.setCompanyStatus(dto.companyStatus());
    entity.setCompanyStatusDesc(dto.companyStatus());
    entity.setAddress1(dto.address1());
    entity.setAddress2(dto.address2());
    entity.setAddress3(dto.address3());
    entity.setCity(dto.city());
    entity.setState(dto.state());
    entity.setPostcode(dto.postcode());
    entity.setCountry(dto.country());
    entity.setMobilePhone(dto.mobilePhone());
    entity.setEmail(dto.email());
    entity.setContactPerson(dto.contactPerson());
    entity.setContactNo(dto.contactNo());
    entity.setCreatedBy(user.userId());
    entity.setUpdatedBy(user.userId());
    entity.setCreatedDate(LocalDateTime.now());
    entity.setUpdatedDate(LocalDateTime.now());
    entity.setActiveFlag(RmsConstant.ACTIVE_FLAG_TRUE);
    requestSupplierRepository.save(entity);
  }

  private void saveItem(ItemMasterDetailDTO dto, RequestHdr hdr, SecUserDTO user) {
    RequestItemMaster entity = new RequestItemMaster();
    entity.setItemGroupCode(dto.itemGroupCode() != null ? trim(dto.itemGroupCode(), 20) : "");
    entity.setGenericNameSeqno(dto.genericNameSeqno());
    entity.setOtherActiveIngredient(trim(dto.otherActiveIngredient(), 100));
    if (dto.strength() != null && !dto.strength().isBlank()) {
      entity.setStrength(new BigDecimal(dto.strength()));
    }
    entity.setDosageSeqno(dto.dosageSeqno());
    entity.setDosageCode(dto.dosageCode() != null ? trim(dto.dosageCode(), 20) : "");
    entity.setItemName(trim(dto.itemName(), 100));
    entity.setItemCategorySeqno(dto.itemCatSeqno());
    entity.setItemCategoryCode(
        dto.itemCategoryCode() != null ? trim(dto.itemCategoryCode(), 20) : "");
    entity.setItemSubgroupSeqno(dto.itemSubgroupSeqno());
    entity.setItemSubgroupCode(
        dto.itemSubgroupCode() != null ? trim(dto.itemSubgroupCode(), 20) : "");
    entity.setRpItemTypeCode(trim(dto.rpItemTypeCode(), 10));
    entity.setRpItemTypeDesc(trim(dto.rpItemTypeDesc(), 100));
    entity.setItemPackagingSeqno(dto.itemPackagingSeqno());
    entity.setItemPackagingCode(
        dto.itemPackagingCode() != null ? trim(dto.itemPackagingCode(), 20) : "");
    entity.setItemPackagingName(trim(dto.itemPackagingName(), 100));
    entity.setSkuSeqno(dto.skuSeqno());
    entity.setSkuAbbr(dto.skuAbbr() != null ? trim(dto.skuAbbr(), 10) : "");
    entity.setPkuSeqno(dto.pkuSeqno());
    entity.setPkuAbbr(dto.pkuAbbr() != null ? trim(dto.pkuAbbr(), 10) : "");
    entity.setMdcNo(trim(dto.mdcNo(), 30));
    entity.setProductSeqno(dto.productSeqno());
    entity.setProductName(dto.productName());
    entity.setManufacturedName(dto.manufacturedName());
    entity.setImporterName(dto.importerName());
    entity.setManufacturedAddress(dto.manufacturedAddress());
    entity.setImporterAddress(dto.importerAddress());
    entity.setGtinNo(dto.gtinNo());
    entity.setMdaNo(dto.mdaNo());
    entity.setConversionFactorNum(dto.conversionFactorNum());
    entity.setPackagingDesc(dto.packagingDesc());
    entity.setCreatedBy(user.userId());
    entity.setUpdatedBy(user.userId());
    entity.setCreatedDate(LocalDateTime.now());
    entity.setUpdatedDate(LocalDateTime.now());
    entity.setActiveFlag(RmsConstant.ACTIVE_FLAG_TRUE);
    entity.setDosageGroupCode("TEMP");
    requestItemMasterRepository.save(entity);
  }

  private void savePackaging(ItemPackagingDetailDTO dto, RequestHdr hdr, SecUserDTO user) {
    RequestItemPackaging entity = new RequestItemPackaging();

    Long nextSeqno = requestDtlRepository.findMaxTransSeqnoByHdrSeqno(hdr.getRequestHdrSeqno());
    Long itemSeqno = (nextSeqno != null ? nextSeqno + 1 : 1L);
    entity.setItemSeqno(itemSeqno);

    entity.setItemName(trim(dto.itemName(), 100));
    entity.setItemCode(dto.itemCode() != null ? trim(dto.itemCode(), 20) : "");
    entity.setItemPackagingReqCode(
        dto.itemPackagingReqCode() != null ? trim(dto.itemPackagingReqCode(), 20) : "");
    entity.setItemPackagingName(trim(dto.itemPackagingName(), 100));
    entity.setSkuSeqno(dto.skuSeqno() != null ? dto.skuSeqno() : 0L);
    entity.setSkuAbbr(dto.skuAbbr() != null ? trim(dto.skuAbbr(), 10) : "");
    entity.setPkuSeqno(dto.pkuSeqno() != null ? dto.pkuSeqno() : 0L);
    entity.setPkuAbbr(dto.pkuAbbr() != null ? trim(dto.pkuAbbr(), 10) : "");
    if (dto.conversionFactor() != null && !dto.conversionFactor().isBlank()) {
      entity.setConversionFactor(new BigDecimal(dto.conversionFactor()));
    }
    entity.setPackagingDesc(trim(dto.packagingDesc(), 200));
    entity.setProductList(trim(dto.productList(), 200));
    entity.setProductSeqno(dto.productSeqno() != null ? dto.productSeqno() : 0L);
    entity.setProductName(trim(dto.productName(), 100));
    entity.setManufacturedName(trim(dto.manufacturedName(), 100));
    entity.setImporterName(trim(dto.importerName(), 100));
    entity.setManufacturedAddress(trim(dto.manufacturedAddress(), 200));
    entity.setImporterAddress(trim(dto.importerAddress(), 200));
    entity.setGtinNo(trim(dto.gtinNo(), 30));
    entity.setMdaNo(trim(dto.mdaNo(), 30));
    entity.setCreatedBy(user.userId());
    entity.setUpdatedBy(user.userId());
    entity.setCreatedDate(LocalDateTime.now());
    entity.setUpdatedDate(LocalDateTime.now());
    entity.setActiveFlag(RmsConstant.ACTIVE_FLAG_TRUE);
    requestItemPackagingRepository.save(entity);
  }

  private void saveFacility(FacilityMasterDetailDTO dto, RequestHdr hdr, SecUserDTO user) {
    RequestFacility entity = new RequestFacility();

    entity.setFacilityReqName(trim(dto.facilityReqName(), 100));
    entity.setFacilityReqGroup(trim(dto.facilityReqGroup(), 50));
    entity.setMinistry(trim(dto.ministry(), 100));
    entity.setFacilityReqCategory(trim(dto.facilityReqCategory(), 50));
    entity.setFacilityReqType(trim(dto.facilityReqType(), 50));

    entity.setAddress1(trim(dto.address1(), 100));
    entity.setAddress2(trim(dto.address2(), 100));
    entity.setAddress3(trim(dto.address3(), 100));
    entity.setCity(trim(dto.city(), 50));
    entity.setState(trim(dto.state(), 10));
    entity.setPostcode(trim(dto.postcode(), 10));
    entity.setCountry(trim(dto.country(), 50));

    entity.setMobilePhone(dto.mobilePhone());
    entity.setEmail(dto.email());
    entity.setContactPerson(dto.contactPerson());
    entity.setContactNo(dto.contactNo());
    entity.setCreatedBy(user.userId());
    entity.setUpdatedBy(user.userId());
    entity.setCreatedDate(LocalDateTime.now());
    entity.setUpdatedDate(LocalDateTime.now());
    entity.setActiveFlag(RmsConstant.ACTIVE_FLAG_TRUE);
    requestFacilityRepository.save(entity);
  }

  @Override
  public List<RequestListDTO> getRequestList(
      RequestListSearchDTO requestDTO, PaginationRequestDTO pgDTO, SecUserDTO user) {
    PaginationRequestDTO pg = PaginationUtil.pageSorting(pgDTO, new RequestMapper(), false);
    return requestRepositoryJooq.getRequestList(requestDTO, pg, user);
  }

  @Override
  public PaginationResponseDTO getRequestListPages(
      RequestListSearchDTO requestDTO, Long size, SecUserDTO user) {
    Long pgSize = requestRepositoryJooq.getRequestListCount(requestDTO);
    return PaginationUtil.pagination(size, pgSize);
  }

  @Override
  public ViewRequestDTO getViewRequest(Long requestHdrSeqno, SecUserDTO user) {
    RequestHeaderDTO header = requestRepositoryJooq.getRequestHeader(requestHdrSeqno);
    if (header == null) {
      throw new RmsException(ExceptionCode.ERROR, "Request not found");
    }
    RequestDtlDTO[] details = header.requestDetails();
    if (details == null || details.length == 0)
      throw new RmsException(ExceptionCode.ERROR, "Request not found");
    Long transSeqno = details[0].transSeqno();
    String transType = details[0].transType();

    ItemMasterRequestDTO item = null;
    ItemPackagingRequestDTO packaging = null;
    FacilityMasterRequestDTO facility = null;
    SupplierMasterRequestDTO supplier = null;

    if ("IM".equalsIgnoreCase(transType)) {
      item = requestRepositoryJooq.getItemMasterRequest(transSeqno);
    } else if ("IP".equalsIgnoreCase(transType)) {
      packaging = requestRepositoryJooq.getItemPackagingRequest(transSeqno);
    } else if ("FM".equalsIgnoreCase(transType)) {
      facility = requestRepositoryJooq.getFacilityRequest(transSeqno);
    } else if ("SM".equalsIgnoreCase(transType)) {
      supplier = requestRepositoryJooq.getSupplierRequest(transSeqno);
    }

    return new ViewRequestDTO(header, details, item, packaging, facility, supplier);
  }
}

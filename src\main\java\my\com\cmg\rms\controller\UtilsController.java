package my.com.cmg.rms.controller;

import java.util.List;
import my.com.cmg.rms.dto.RefCodesDTO;
import my.com.cmg.rms.service.IUtilsService;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/v1/rms/utils")
public class UtilsController {
  IUtilsService utilsService;

  public UtilsController(IUtilsService utilsService) {
    this.utilsService = utilsService;
  }

  /**
   * Get the list of Request Types.
   *
   * @return a list of RefCodesDTO where each entry represents an Request Type with its code and
   *     description.
   */
  @GetMapping("/request_type")
  public List<RefCodesDTO> getRequestType(Authentication authentication) {
    // System.out.println((SecUserDTO) authentication.getPrincipal());
    List<RefCodesDTO> response = utilsService.getRequestType();
    return response;
  }

  /**
   * Get the list of Category.
   *
   * @return a list of RefCodesDTO where each entry represents an Category with its code and
   *     description.
   */
  @GetMapping("/category")
  public List<RefCodesDTO> getCategory() {
    List<RefCodesDTO> response = utilsService.getCategory();
    return response;
  }

  /**
   * Get the list of Sub Category based on the provided Category.
   *
   * @return a list of RefCodesDTO where each entry represents an Sub Category with its code and
   *     description.
   */
  // /api/v1/rms/utils/IM/sub_category
  @GetMapping("/{category}/sub_category")
  public List<RefCodesDTO> getSubCategory(@PathVariable("category") String category) {
    List<RefCodesDTO> response = utilsService.getSubCategory(category);
    return response;
  }

  /**
   * Get the list of Assignment.
   *
   * @return a list of RefCodesDTO where each entry represents an Assignment with its code and
   *     description.
   */
  @GetMapping("/assignment")
  public List<RefCodesDTO> getAssignment() {
    List<RefCodesDTO> response = utilsService.getAssignment();
    return response;
  }

  /**
   * Get the list of Status.
   *
   * @return a list of RefCodesDTO where each entry represents an Status with its code and
   *     description.
   */
  @GetMapping("/status")
  public List<RefCodesDTO> getStatus() {
    List<RefCodesDTO> response = utilsService.getStatus();
    return response;
  }

  /**
   * Get the list of Intention.
   *
   * @return a list of RefCodesDTO where each entry represents an Intention with its code and
   *     description.
   */
  @GetMapping("/intention")
  public List<RefCodesDTO> getIntention() {
    List<RefCodesDTO> response = utilsService.getIntention();
    return response;
  }

  /**
   * Get the list of Dossage Form.
   *
   * @return a list of DosageFormDTO where each entry represents an Dossage Form with its code and
   *     description.
   */
  // dosage form dropdown
  @GetMapping("/dosage_form") // done
  public List<RefCodesDTO> getAllDosage() {
    List<RefCodesDTO> response = utilsService.getAllDosage();
    return response;
  }

  /**
   * Get the list of item class.
   *
   * @return a list of RefCodesDTO where each entry represents an item class with its code and
   *     description.
   */
  @GetMapping("/item_class")
  public List<RefCodesDTO> getItemClass() {
    List<RefCodesDTO> response = utilsService.getItemClass();
    return response;
  }

  /**
   * Get the list of item subclass.
   *
   * @return a list of RefCodesDTO where each entry represents an item subclass with its code and
   *     description.
   */
  @GetMapping("/item_sub_class")
  public List<RefCodesDTO> getItemSubClass() {
    List<RefCodesDTO> response = utilsService.getItemSubClass();
    return response;
  }

  /**
   * Get the list of State.
   *
   * @return a list of RefCodesDTO where each entry represents an State with its code and
   *     description.
   */
  @GetMapping("/state")
  public List<RefCodesDTO> getState() {
    List<RefCodesDTO> response = utilsService.getState();
    return response;
  }

  /**
   * Get the list of Item Group.
   *
   * @return a list of RefCodesDTO where each entry represents an Item Group with its code and
   *     description.
   */
  @GetMapping("/item_group")
  public List<RefCodesDTO> getItemGroup() {
    List<RefCodesDTO> response = utilsService.getItemGroup();
    return response;
  }

  /**
   * Get the list of Drug Schedule.
   *
   * @return a list of RefCodesDTO where each entry represents an Drug Schedule with its code and
   *     description.
   */
  @GetMapping("/drug_schedule")
  public List<RefCodesDTO> getDrugSchedule() {
    List<RefCodesDTO> response = utilsService.getDrugSchedule();
    return response;
  }

  /**
   * Get the list of Drug Type.
   *
   * @return a list of RefCodesDTO where each entry represents an Drug Type with its code and
   *     description.
   */
  @GetMapping("/drug_type")
  public List<RefCodesDTO> getDrugType() {
    List<RefCodesDTO> response = utilsService.getDrugType();
    return response;
  }

  /**
   * Get the list of Special Order Configuration.
   *
   * @return a list of RefCodesDTO where each entry represents an Special Order Configuration with
   *     its code and description.
   */
  @GetMapping("/special_order_configuration")
  public List<RefCodesDTO> getSpecialOrderConfiguration() {
    List<RefCodesDTO> response = utilsService.getSpecialOrderConfiguration();
    return response;
  }

  /**
   * Get the list of Sku.
   *
   * @return a list of RefCodesDTO where each entry represents an Sku with its code and description.
   */
  @GetMapping("/sku")
  public List<RefCodesDTO> getSku() {
    List<RefCodesDTO> response = utilsService.getSku();
    return response;
  }

  /**
   * Get the list of Pku.
   *
   * @return a list of RefCodesDTO where each entry represents an Pku with its code and description.
   */
  @GetMapping("/pku")
  public List<RefCodesDTO> getPku() {
    List<RefCodesDTO> response = utilsService.getPku();
    return response;
  }

  /**
   * Get the list of Radiopharmaceutical Item.
   *
   * @return a list of RefCodesDTO where each entry represents an Radiopharmaceutical Item with its
   *     code and description.
   */
  @GetMapping("/radiopharmaceutical_item")
  public List<RefCodesDTO> getRadiopharmaceuticalItem() {
    List<RefCodesDTO> response = utilsService.getRadiopharmaceuticalItem();
    return response;
  }

  /**
   * Get the list of Facility Group.
   *
   * @return a list of RefCodesDTO where each entry represents an Facility Group` with its code and
   *     description.
   */
  @GetMapping("/facility_group")
  public List<RefCodesDTO> getFacilityGroup() {
    List<RefCodesDTO> response = utilsService.getFacilityCategory();
    return response;
  }

  /**
   * Get the list of Ministry.
   *
   * @return a list of RefCodesDTO where each entry represents an Ministry with its code and
   *     description.
   */
  @GetMapping("/ministry")
  public List<RefCodesDTO> getMinistry() {
    List<RefCodesDTO> response = utilsService.getMinistry();
    return response;
  }

  @GetMapping("/facility_category")
  public List<RefCodesDTO> getFacilityCategory() {
    List<RefCodesDTO> response = utilsService.getFacilityCategory();
    return response;
  }

  /**
   * Get the list of Facility Type.
   *
   * @return a list of RefCodesDTO where each entry represents an Facility Type with its code and
   *     description.
   */
  @GetMapping("/facility_type")
  public List<RefCodesDTO> getFacilityType() {
    List<RefCodesDTO> response = utilsService.getFacilityType();
    return response;
  }

  /**
   * Get the list of Company Status.
   *
   * @return a list of RefCodesDTO where each entry represents an Company Status with its code and
   *     description.
   */
  @GetMapping("/company_status")
  public List<RefCodesDTO> getCompanyStatus() {
    List<RefCodesDTO> response = utilsService.getCompanyStatus();
    return response;
  }

  /**
   * Get the list of Item Detail.
   *
   * @return a list of RefCodesDTO where each entry represents an Item Detail with its code and
   *     description.
   */
  @GetMapping("/item_detail")
  public List<RefCodesDTO> getItemDetail() {
    List<RefCodesDTO> response = utilsService.getItemDetail();
    return response;
  }

  /**
   * Get the list of Division.
   *
   * @return a list of RefCodesDTO where each entry represents an Division with its code and
   *     description.
   */
  @GetMapping("/division")
  public List<RefCodesDTO> getDivision() {
    List<RefCodesDTO> response = utilsService.getDivision();
    return response;
  }
}

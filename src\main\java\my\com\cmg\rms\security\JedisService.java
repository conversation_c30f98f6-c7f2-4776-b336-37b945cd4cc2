package my.com.cmg.rms.security;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import redis.clients.jedis.*;

@Service
@Slf4j
public class JedisService {

  private JedisPool jedisPool;

  public static String JEDIS = "-------JEDIS------- | {}";

  public JedisService(
      @Value("${phis.redis.host}") String redisHost, @Value("${phis.redis.port}") int redisPort) {

    log.info(JEDIS, "Redis Host: " + redisHost);
    log.info(JEDIS, "Redis Port: " + redisPort);
    this.jedisPool = new JedisPool(new JedisPoolConfig(), redisHost, redisPort);
  }

  public String getHashData(String key, String field) {
    // Get the pool and use the database
    try (Jedis jedis = jedisPool.getResource()) {

      String value = jedis.hget(key, field);
      log.info(JEDIS, "Key: " + key);
      log.info(JEDIS, "Field: " + field);
      log.info(JEDIS, "Value: " + value);
      return value;
    }
  }
}

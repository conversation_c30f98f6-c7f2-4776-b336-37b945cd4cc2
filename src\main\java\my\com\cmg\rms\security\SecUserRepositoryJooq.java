package my.com.cmg.rms.security;

import static org.jooq.impl.DSL.*;

import lombok.extern.slf4j.Slf4j;
import my.com.cmg.rms.utils.LogUtil;
import org.jooq.DSLContext;
import org.springframework.stereotype.Repository;

@Repository
@Slf4j
public class SecUserRepositoryJooq {

  private final DSLContext dsl;

  public SecUserRepositoryJooq(DSLContext dsl) {
    this.dsl = dsl;
  }

  public SecUserDTO findByUserId(Long userId) {
    var query =
        dsl.select(
                field("user_id", Long.class).as("userId"),
                field("username", String.class).as("username"),
                field("user_full_name", String.class).as("userFullName"),
                field("identity_type", String.class).as("identityType"),
                field("identity_no", String.class).as("identityNo"),
                field("user_email", String.class).as("userEmail"),
                field("user_contact_no", String.class).as("userContactNo"),
                field("user_designation", String.class).as("userDesignation"),
                field("user_designation_desc", String.class).as("userDesignationDesc"),
                field("user_location_type", String.class).as("userLocationType"),
                field("user_location_type_desc", String.class).as("userLocationDesc"),
                field("division", String.class).as("division"),
                field("division_desc", String.class).as("divisionDesc"),
                field("jkn", String.class).as("jkn"),
                field("jkn_desc", String.class).as("jknDesc"),
                field("facility_seqno", Long.class).as("facilitySeqno"),
                field("facility_desc", String.class).as("facilityDesc"),
                field("user_type", String.class).as("userType"),
                field("user_type_desc", String.class).as("userTypeDesc"),
                field("dept_seqno", Long.class).as("deptSeqno"),
                field("dept_desc", String.class).as("deptDesc"),
                field("prescriber_seqno", Long.class).as("prescriberSeqno"),
                field("prescriber_desc", String.class).as("prescriberDesc"),
                field("registration_seqno", Long.class).as("registrationSeqno"),
                field("specialty_seqno", Long.class).as("specialtySeqno"),
                field("specialty_desc", String.class).as("specialtyDesc"),
                field("keycloak_user_id", String.class).as("keycloakUserId"),
                field("pw_reset_key", String.class).as("pwResetKey"))
            .from("rm_user")
            .where(field("user_id", Long.class).eq(userId));

    log.info(LogUtil.QUERY, query);
    return query.fetchOneInto(SecUserDTO.class);
  }
}

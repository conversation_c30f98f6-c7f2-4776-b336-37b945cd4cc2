package my.com.cmg.rms.dto.requestDetail;

import java.math.BigDecimal;

public record ItemMasterDetailDTO(
    String itemGroupCode,
    Long itemPackagingSeqno,
    Long genericNameSeqno,
    String otherActiveIngredient,
    String strength,
    Long dosageSeqno,
    String dosageCode,
    String itemName,
    Long itemCatSeqno,
    String itemCategoryCode,
    Long itemSubgroupSeqno,
    Long freqSeqno,
    String administrationRoute,
    String drugIndication,
    String rpItemTypeCode,
    String rpItemTypeDesc,
    String itemSubgroupCode,
    String itemPackagingCode,
    String itemPackagingName,
    Long skuSeqno,
    String skuAbbr,
    Long pkuSeqno,
    String pkuAbbr,
    BigDecimal conversionFactorNum,
    String packagingDesc,
    String mdcNo,
    Long productSeqno,
    String productName,
    String manufacturedName,
    String importerName,
    String manufacturedAddress,
    String importerAddress,
    String gtinNo,
    String mdaNo) {}

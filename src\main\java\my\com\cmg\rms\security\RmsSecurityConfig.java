package my.com.cmg.rms.security;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.oauth2.jwt.JwtDecoders;
import org.springframework.security.web.SecurityFilterChain;

@Configuration
@EnableWebSecurity
@EnableMethodSecurity
public class RmsSecurityConfig {

  // @Value("${spring.security.oauth2.resourceserver.jwt.issuer-uri}")
  String issuerUri;
  String developmentMode;

  private SecUserService userService;

  public RmsSecurityConfig(
      @Value("${spring.security.oauth2.resourceserver.jwt.issuer-uri}") String issuerUri,
      @Value("${phis.development.mode}") String developmentMode,
      SecUserService userService) {
    this.issuerUri = issuerUri;
    this.userService = userService;
    this.developmentMode = developmentMode;
  }

  @Bean
  public SecurityFilterChain configurePatientFilterChain(HttpSecurity http) throws Exception {
    if (developmentMode.equals("true")) {
      // Permit all requests in development mode
      http.authorizeHttpRequests(authorize -> authorize.anyRequest().permitAll());
    } else {
      // Secure the application in production mode
      http.authorizeHttpRequests(
              authorize ->
                  authorize
                      // Add any specific matchers you want to permit
                      .anyRequest()
                      .authenticated())
          .oauth2ResourceServer(
              oauth2 ->
                  oauth2.jwt(
                      jwt -> {
                        jwt.decoder(JwtDecoders.fromIssuerLocation(issuerUri));
                        // jwt.jwtAuthenticationConverter(new CustomJwtConverter(userService));
                      }));
    }

    http.csrf(csrf -> csrf.disable());

    return http.build();
  }

  @Bean
  public FilterRegistrationBean<UserIdFilter> userFilter(JedisService jedisService) {
    FilterRegistrationBean<UserIdFilter> registrationBean = new FilterRegistrationBean<>();
    registrationBean.setFilter(new UserIdFilter(userService, jedisService));
    registrationBean.setOrder(Integer.MAX_VALUE);
    if (developmentMode.equals("true")) {
      registrationBean.setEnabled(false);
    }
    return registrationBean;
  }
}

package my.com.cmg.rms.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "rm_user")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class RmUser extends BaseEntity {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "user_id")
  private Long userId;

  @Column(name = "username")
  private String username;

  @Column(name = "password")
  private String password;

  @Column(name = "user_full_name")
  private String userFullName;

  @Column(name = "identity_type")
  private String identityType;

  @Column(name = "identity_no")
  private String identityNo;

  @Column(name = "user_email")
  private String userEmail;

  @Column(name = "user_contact_no")
  private String userContactNo;

  @Column(name = "user_designation")
  private String userDesignation;

  @Column(name = "user_designation_desc")
  private String userDesignationDesc;

  @Column(name = "user_location_type")
  private String userLocationType;

  @Column(name = "user_location_type_desc")
  private String userLocationTypeDesc;

  @Column(name = "division")
  private String division;

  @Column(name = "division_desc")
  private String divisionDesc;

  @Column(name = "jkn")
  private String jkn;

  @Column(name = "jkn_desc")
  private String jknDesc;

  @Column(name = "facility_seqno")
  private Long facilitySeqno;

  @Column(name = "facility_desc")
  private String facilityDesc;

  @Column(name = "user_type")
  private String userType;

  @Column(name = "user_type_desc")
  private String userTypeDesc;

  @Column(name = "dept_seqno")
  private Long deptSeqno;

  @Column(name = "dept_desc")
  private String deptDesc;

  @Column(name = "prescriber_seqno")
  private Long prescriberSeqno;

  @Column(name = "prescriber_desc")
  private String prescriberDesc;

  @Column(name = "registration_seqno")
  private Long registrationSeqno;

  @Column(name = "specialty_seqno")
  private Long specialtySeqno;

  @Column(name = "specialty_desc")
  private String specialtyDesc;

  @Column(name = "keycloak_user_id")
  private String keycloakUserId;

  @Column(name = "pw_reset_key")
  private String pwResetKey;
}

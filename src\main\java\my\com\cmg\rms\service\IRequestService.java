package my.com.cmg.rms.service;

import java.util.List;
import my.com.cmg.rms.dto.paging.PaginationRequestDTO;
import my.com.cmg.rms.dto.paging.PaginationResponseDTO;
import my.com.cmg.rms.dto.requestDetail.SaveRequestDTO;
import my.com.cmg.rms.dto.requestListing.RequestListDTO;
import my.com.cmg.rms.dto.requestListing.RequestListSearchDTO;
import my.com.cmg.rms.dto.viewDetail.ViewRequestDTO;
import my.com.cmg.rms.security.SecUserDTO;

public interface IRequestService {

  Long save(SaveRequestD<PERSON> dto, SecUserDTO user);

  List<RequestListDTO> getRequestList(
      RequestListSearchDTO requestDTO, PaginationRequestDTO pgDTO, SecUserDTO user);

  PaginationResponseDTO getRequestListPages(
      RequestListSearchDTO requestDTO, Long size, SecUserDTO user);

  ViewRequestDTO getViewRequest(Long requestHdrSeqno, SecUserDTO user);

  void update(Long requestHdrSeqno, SaveRequestDTO dto, SecUserDTO user);

  void confirmRequest(Long requestHdrSeqno, SecUserDTO user);
}

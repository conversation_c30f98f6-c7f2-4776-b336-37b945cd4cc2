package my.com.cmg.rms.model;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Table(name = "rm_request_dtl")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class RequestDtl extends BaseEntity {

  @Id
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "request_dtl_seq")
  @SequenceGenerator(
      name = "request_dtl_seq",
      sequenceName = "rm_request_dtl_seq",
      allocationSize = 1)
  @Column(name = "request_dtl_seqno", nullable = false)
  private Long requestDtlSeqno;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "request_hdr_seqno", nullable = false)
  private RequestHdr requestHdr;

  @Column(name = "trans_seqno", nullable = false)
  private Long transSeqno;

  @Column(name = "trans_code", length = 50)
  private String transCode;

  @Column(name = "trans_name", length = 100)
  private String transName;

  @Column(name = "trans_type", length = 50)
  private String transType;

  @Column(name = "trans_details", length = 200)
  private String transDetails;
}

package my.com.cmg.rms.controller;

import java.time.LocalDate;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.cmg.rms.dto.paging.PaginationRequestDTO;
import my.com.cmg.rms.dto.paging.PaginationResponseDTO;
import my.com.cmg.rms.dto.requestDetail.SaveRequestDTO;
import my.com.cmg.rms.dto.requestListing.RequestListDTO;
import my.com.cmg.rms.dto.requestListing.RequestListSearchDTO;
import my.com.cmg.rms.dto.viewDetail.ViewRequestDTO;
import my.com.cmg.rms.security.SecUserDTO;
import my.com.cmg.rms.service.ICommonService;
import my.com.cmg.rms.service.IRequestService;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping("/api/v1/rms/request")
public class RequestController {

  private final IRequestService requestService;
  private final ICommonService commonService;

  @GetMapping("/list")
  public List<RequestListDTO> getRequestList(
      @RequestParam(required = false) String requestNo,
      @RequestParam(required = false) Long facilitySeqno,
      @RequestParam(required = false) String requestType,
      @RequestParam(required = false) String title,
      @RequestParam(required = false) String assignedTo,
      @RequestParam(required = false) String category,
      @RequestParam(required = false) LocalDate requestDateFrom,
      @RequestParam(required = false) LocalDate requestDateTo,
      @RequestParam(required = false) String status,
      @RequestParam(required = false) String sort,
      @RequestParam(required = false) String sortDirection,
      @RequestParam(required = false) Long page,
      @RequestParam(required = false) Long size,
      Authentication authentication) {
    SecUserDTO secUser = commonService.getAuthenticatedUser(authentication);
    PaginationRequestDTO pgDTO = new PaginationRequestDTO(sort, sortDirection, page, size);
    RequestListSearchDTO requestDTO =
        new RequestListSearchDTO(
            requestNo,
            facilitySeqno,
            requestType,
            title,
            assignedTo,
            category,
            requestDateFrom,
            requestDateTo,
            status);

    return requestService.getRequestList(requestDTO, pgDTO, secUser);
  }

  @GetMapping("/list/page")
  public PaginationResponseDTO getRequestListPages(
      @RequestParam(required = false) String requestNo,
      @RequestParam(required = false) Long facilitySeqno,
      @RequestParam(required = false) String requestType,
      @RequestParam(required = false) String title,
      @RequestParam(required = false) String assignedTo,
      @RequestParam(required = false) String category,
      @RequestParam(required = false) LocalDate requestDateFrom,
      @RequestParam(required = false) LocalDate requestDateTo,
      @RequestParam(required = false) String status,
      @RequestParam(required = false) Long size,
      Authentication authentication) {
    SecUserDTO secUser = commonService.getAuthenticatedUser(authentication);
    RequestListSearchDTO requestDTO =
        new RequestListSearchDTO(
            requestNo,
            facilitySeqno,
            requestType,
            title,
            assignedTo,
            category,
            requestDateFrom,
            requestDateTo,
            status);

    return requestService.getRequestListPages(requestDTO, size, secUser);
  }

  @PostMapping("/saveRequest")
  public void saveRequest(@RequestBody SaveRequestDTO dto, Authentication authentication) {
    SecUserDTO secUser = commonService.getAuthenticatedUser(authentication);
    requestService.save(dto, secUser);
  }

  @GetMapping("/{requestHdrSeqno}")
  public ViewRequestDTO getViewRequest(
      @PathVariable Long requestHdrSeqno, Authentication authentication) {
    SecUserDTO secUser = commonService.getAuthenticatedUser(authentication);
    return requestService.getViewRequest(requestHdrSeqno, secUser);
  }

  @PutMapping("/updateRequest/{requestHdrSeqno}")
  public void updateRequest(
      @PathVariable Long requestHdrSeqno,
      @RequestBody SaveRequestDTO dto,
      Authentication authentication) {
    SecUserDTO secUser = commonService.getAuthenticatedUser(authentication);
    requestService.update(requestHdrSeqno, dto, secUser);
  }

  @PutMapping("/confirmRequest/{requestHdrSeqno}")
  public void confirmRequest(@PathVariable Long requestHdrSeqno, Authentication authentication) {
    SecUserDTO secUser = commonService.getAuthenticatedUser(authentication);
    requestService.confirmRequest(requestHdrSeqno, secUser);
  }
}
